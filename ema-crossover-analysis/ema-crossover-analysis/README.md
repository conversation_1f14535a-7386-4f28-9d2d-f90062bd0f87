# EMA Crossover Analysis Tool

A comprehensive Python solution for analyzing minute-based candle data to detect EMA 5 and EMA 10 crossovers for generating buy/sell signals.

## Features

- 📊 **1-minute candle data analysis** - Processes minute-level OHLCV data
- 📈 **EMA calculations** - Computes EMA 5 and EMA 10 using pandas_ta
- 🎯 **Crossover detection** - Identifies buy/sell signals based on EMA crossovers
- 💾 **CSV output** - Generates two CSV files with candle data and signals
- 🔌 **Dhan API integration** - Supports live data fetching from Dhan API
- 🧪 **Sample data generation** - Creates realistic test data when live API is unavailable

## Signal Logic

- **BUY Signal**: When EMA 5 crosses above EMA 10 (bullish crossover)
- **SELL Signal**: When EMA 5 crosses below EMA 10 (bearish crossover)

## Installation

This project uses UV for dependency management. Make sure you have UV installed.

### 1. Clone or navigate to the project directory

```bash
cd ema-crossover-analysis
```

### 2. Install dependencies

```bash
uv sync
```

## Usage

### Basic Usage (with sample data)

```bash
uv run main.py
```

This will:
1. Attempt to fetch 2 days of real 1-minute candle data for NIFTY from Dhan API
2. Filter data for market hours only (9:15 AM to 3:30 PM IST)
3. Calculate EMA 5 and EMA 10 using only close prices
4. Detect crossovers with improved validation
5. Save results to CSV files in the `output/` directory

**Note**: If Dhan API credentials are not configured, it will automatically fall back to sample data.

### Using Live Dhan API Data

#### Option 1: Interactive Setup (Recommended)

```bash
uv run setup_credentials.py
```

This will prompt you for your Dhan credentials and automatically configure the system.

#### Option 2: Manual Configuration

1. Edit `config.py` and update your credentials:

```python
DHAN_CLIENT_CODE = "your_dhan_client_code"
DHAN_TOKEN_ID = "your_dhan_token_id"
USE_SAMPLE_DATA = False
```

2. Run the analysis:

```bash
uv run main.py
```

#### Testing Your Setup

```bash
uv run setup_credentials.py test
```

### Customizing Analysis Parameters

Edit the configuration section in `main.py`:

```python
SYMBOL = "NIFTY"        # Trading symbol
EXCHANGE = "INDEX"      # Exchange name
USE_SAMPLE_DATA = True  # Use sample data or live API
SAMPLE_DAYS = 5         # Days of sample data to generate
```

## Output Files

The tool generates two CSV files in the `output/` directory:

### 1. `candle_data.csv`
Contains all 1-minute candle data with columns:
- `timestamp` - Date and time of the candle
- `open` - Opening price
- `high` - Highest price
- `low` - Lowest price
- `close` - Closing price
- `volume` - Trading volume

### 2. `signals.csv`
Contains only the buy/sell signals with columns:
- `timestamp` - When the signal occurred
- `signal` - BUY or SELL
- `price` - Price at signal time
- `ema_5_value` - EMA 5 value at signal time
- `ema_10_value` - EMA 10 value at signal time

## Dependencies

- `pandas` - Data manipulation and analysis
- `numpy` - Numerical computing
- `pandas-ta` - Technical analysis indicators
- `Dhan_Tradehull` - Dhan API integration
- Additional dependencies for API connectivity

## Project Structure

```
ema-crossover-analysis/
├── main.py              # Main analysis script
├── pyproject.toml       # Project configuration
├── uv.lock             # Dependency lock file
├── README.md           # This file
└── output/             # Generated CSV files (created when run)
    ├── candle_data.csv
    └── signals.csv
```

## Example Output

```
🎯 EMA Crossover Analysis Tool
==================================================
📊 Generating sample data for NIFTY (5 days)
✓ Generated 1875 data points
📈 Calculating EMA 5 and EMA 10
✓ EMA calculations completed. 1866 valid data points.
🔍 Detecting EMA crossovers
✓ Crossover detection completed:
  📈 Buy signals: 23
  📉 Sell signals: 22
💾 Candle data saved to: output/candle_data.csv
   📊 Records: 1866
💾 Signals data saved to: output/signals.csv
   📊 Signal records: 45

📊 ANALYSIS SUMMARY
------------------------------
Total data points: 1866
Buy signals: 23
Sell signals: 22
Signal frequency: 2.41%

🎯 RECENT SIGNALS:
  📈 2024-12-21 14:45:00+05:30: BUY at 24089.45
  📉 2024-12-21 15:12:00+05:30: SELL at 24076.23
==================================================
✅ Analysis completed successfully!
```

## Notes

- The tool automatically falls back to sample data if Dhan API is unavailable
- EMA calculations require at least 10 data points, so the first few rows are dropped
- Sample data includes realistic price movements with trend and volatility
- Market hours are simulated as 9:15 AM to 3:30 PM (Indian market hours)
- Weekends are automatically excluded from sample data generation

## Troubleshooting

1. **Import errors**: Make sure all dependencies are installed with `uv sync`
2. **API connection issues**: Verify your Dhan credentials and network connection
3. **No signals generated**: Try increasing the sample data period or check if EMA periods are appropriate for your data timeframe