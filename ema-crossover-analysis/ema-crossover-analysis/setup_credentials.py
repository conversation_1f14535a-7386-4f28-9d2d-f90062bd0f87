#!/usr/bin/env python3
"""
Setup script to configure Dhan API credentials
==============================================

This script helps you set up your Dhan API credentials for real data fetching.
"""

import os
import sys

def setup_credentials():
    """Interactive setup for Dhan API credentials"""
    
    print("🔧 Dhan API Credentials Setup")
    print("=" * 40)
    print()
    print("To fetch real market data, you need Dhan API credentials.")
    print("Get these from your Dhan account:")
    print("1. Login to Dhan web platform")
    print("2. Go to API section")
    print("3. Generate API credentials")
    print()
    
    # Get credentials from user
    client_code = input("Enter your Dhan Client Code: ").strip()
    token_id = input("Enter your Dhan Token ID: ").strip()
    
    if not client_code or not token_id:
        print("❌ Both Client Code and Token ID are required!")
        return False
    
    # Read current config file
    config_file = "config.py"
    
    try:
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Update credentials in config
        content = content.replace(
            'DHAN_CLIENT_CODE = None',
            f'DHAN_CLIENT_CODE = "{client_code}"'
        )
        content = content.replace(
            'DHAN_TOKEN_ID = None',
            f'DHAN_TOKEN_ID = "{token_id}"'
        )
        
        # Write updated config
        with open(config_file, 'w') as f:
            f.write(content)
        
        print()
        print("✅ Credentials saved successfully!")
        print(f"📝 Updated {config_file}")
        print()
        print("🚀 You can now run the analysis with real data:")
        print("   uv run main.py")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ Config file {config_file} not found!")
        return False
    except Exception as e:
        print(f"❌ Error updating config: {e}")
        return False

def test_credentials():
    """Test the configured credentials"""
    
    print("🧪 Testing Dhan API Connection")
    print("=" * 40)
    
    try:
        from config import get_config
        config = get_config()
        
        client_code = config['dhan_client_code']
        token_id = config['dhan_token_id']
        
        if not client_code or not token_id:
            print("❌ Credentials not configured. Run setup first.")
            return False
        
        print(f"Client Code: {client_code}")
        print(f"Token ID: {token_id[:10]}..." if len(token_id) > 10 else token_id)
        
        # Try to import and initialize Dhan
        try:
            from Dhan_Tradehull import Tradehull
            tsl = Tradehull(client_code, token_id)
            print("✅ Dhan API connection successful!")
            return True
        except Exception as e:
            print(f"❌ Dhan API connection failed: {e}")
            return False
            
    except ImportError:
        print("❌ Config file not found or invalid")
        return False

def main():
    """Main setup function"""
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_credentials()
    else:
        setup_credentials()

if __name__ == "__main__":
    main()
