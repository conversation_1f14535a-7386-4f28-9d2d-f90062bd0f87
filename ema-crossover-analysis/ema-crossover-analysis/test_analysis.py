#!/usr/bin/env python3
"""
Test script for EMA Crossover Analysis
"""

import pandas as pd
import os
from main import EMACrossoverAnalyzer

def test_sample_data_generation():
    """Test sample data generation"""
    print("🧪 Testing sample data generation...")
    
    analyzer = EMACrossoverAnalyzer()
    data = analyzer.generate_sample_data("NIFTY", 2)
    
    assert len(data) > 0, "Sample data should not be empty"
    assert 'timestamp' in data.columns, "Should have timestamp column"
    assert 'close' in data.columns, "Should have close column"
    
    print(f"✓ Generated {len(data)} sample data points")

def test_ema_calculation():
    """Test EMA calculation"""
    print("🧪 Testing EMA calculation...")
    
    analyzer = EMACrossoverAnalyzer()
    
    # Create simple test data
    test_data = pd.DataFrame({
        'timestamp': ['2024-01-01 09:15:00+05:30', '2024-01-01 09:16:00+05:30', 
                     '2024-01-01 09:17:00+05:30', '2024-01-01 09:18:00+05:30',
                     '2024-01-01 09:19:00+05:30'],
        'open': [100, 101, 102, 103, 104],
        'high': [101, 102, 103, 104, 105],
        'low': [99, 100, 101, 102, 103],
        'close': [100, 101, 102, 103, 104],
        'volume': [1000, 1000, 1000, 1000, 1000]
    })
    
    result = analyzer.calculate_ema(test_data)
    
    assert 'ema_5' in result.columns, "Should have EMA 5 column"
    assert 'ema_10' in result.columns, "Should have EMA 10 column"
    assert len(result) > 0, "Should have calculated EMA values"
    
    print(f"✓ EMA calculation successful with {len(result)} data points")

def test_crossover_detection():
    """Test crossover detection"""
    print("🧪 Testing crossover detection...")
    
    analyzer = EMACrossoverAnalyzer()
    
    # Create test data with known crossover
    test_data = pd.DataFrame({
        'timestamp': [f'2024-01-01 09:{15+i}:00+05:30' for i in range(20)],
        'open': [100 + i for i in range(20)],
        'high': [101 + i for i in range(20)],
        'low': [99 + i for i in range(20)],
        'close': [100 + i for i in range(20)],  # Increasing prices
        'volume': [1000] * 20
    })
    
    # Calculate EMAs
    data_with_ema = analyzer.calculate_ema(test_data)
    
    # Detect crossovers
    result = analyzer.detect_crossovers(data_with_ema)
    
    assert 'signal' in result.columns, "Should have signal column"
    assert 'buy_signal' in result.columns, "Should have buy_signal column"
    assert 'sell_signal' in result.columns, "Should have sell_signal column"
    
    print(f"✓ Crossover detection successful")

def test_file_output():
    """Test CSV file output"""
    print("🧪 Testing file output...")
    
    analyzer = EMACrossoverAnalyzer()
    
    # Generate sample data and run analysis
    candle_file, signals_file = analyzer.run_analysis(
        symbol="TEST",
        use_sample_data=True,
        days=1
    )
    
    # Check if files exist
    assert os.path.exists(candle_file), f"Candle file should exist: {candle_file}"
    assert os.path.exists(signals_file), f"Signals file should exist: {signals_file}"
    
    # Check file contents
    candle_df = pd.read_csv(candle_file)
    signals_df = pd.read_csv(signals_file)
    
    assert len(candle_df) > 0, "Candle data should not be empty"
    assert 'timestamp' in candle_df.columns, "Candle data should have timestamp"
    assert 'close' in candle_df.columns, "Candle data should have close price"
    
    if len(signals_df) > 0:
        assert 'signal' in signals_df.columns, "Signals should have signal column"
        assert 'price' in signals_df.columns, "Signals should have price column"
    
    print(f"✓ File output successful")
    print(f"  📊 Candle records: {len(candle_df)}")
    print(f"  🎯 Signal records: {len(signals_df)}")

def main():
    """Run all tests"""
    print("🚀 Running EMA Crossover Analysis Tests")
    print("=" * 50)
    
    try:
        test_sample_data_generation()
        test_ema_calculation()
        test_crossover_detection()
        test_file_output()
        
        print("=" * 50)
        print("✅ All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
